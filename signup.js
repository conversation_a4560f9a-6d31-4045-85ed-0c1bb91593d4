import { createUserWithEmailAndPassword, getAuth } from "https://www.gstatic.com/firebasejs/11.10.0/firebase-auth.js";

const auth = getAuth();

const form = document.getElementById("signupForm");
const message = document.getElementById("message");

form.addEventListener("submit", async (e) => {
  e.preventDefault();

  const email = document.getElementById("email").value.trim();
  const password = document.getElementById("password").value;

  try {
    await createUserWithEmailAndPassword(auth, email, password);
    message.style.color = "lightgreen";
    message.textContent = "Signup successful! Redirecting to login...";
    setTimeout(() => {
      window.location.href = "login.html";
    }, 2000);
  } catch (error) {
    message.style.color = "red";
    message.textContent = error.message;
  }
});
