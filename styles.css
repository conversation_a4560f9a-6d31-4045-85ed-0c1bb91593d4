 :root {
  /* Dark Theme Defaults */
  --background-color: #121212;
  --text-color: #ffffff;
  --link-color: #ffffff;
  --link-hover-color: #4CAF50;
  --border-color: #333;
}

.light-theme {
  /* Light Theme Overrides */
  --background-color: #f0f0f0;
  --text-color: #222222;
  --link-color: #222222;
  --link-hover-color: #4CAF50;
  --border-color: #ddd;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body, html {
  height: 100%;
  width: 100%;
  background-color: var(--background-color);
  color: var(--text-color);
  font-family: 'Poppins', sans-serif;
}

a {
  text-decoration: none;
  color: var(--link-color);
  transition: color 0.3s ease;
}

a:hover {
  color: var(--link-hover-color);
}

.container {
  padding: 16px;
  min-height: 10vh;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1px;
}

.nav-left {
  display: flex;
  align-items: center;
  gap: 15px;
}

.home-link {
  color: var(--link-color);
}

.search-box input {
  padding: 8px 14px;
  border-radius: 20px;
  border: 2px solid var(--text-color);
  background: transparent;
  color: var(--text-color);
  outline: none;
  display: flex;
  align-items: center;
}

.nav-right a {
  margin-left: 20px;
  color: var(--link-color);
  font-weight: bold;
}

.section-title {
  margin: 10px 0;
  font-size: 20px;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: 10px;
}

.trending {
  display: flex;
  gap: 30px;
  margin-bottom: 40px;
  color: var(--text-color);
  padding: 16px;
}

.box {
  width: 200px;
  height: 200px;
  background-color: lightgray;
  border: 2px solid transparent;
}

.box img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
  display: block;
}

.box:hover img {
  transform: scale(1.05);
  transition: transform 0.3s ease;
}

.box.active {
  border: 2px solid #00f;
}

.artists {
  display: flex;
  gap: 30px;
  margin-bottom: 40px;
  padding: 16px;
  margin-left: 10px;
}

.circle {
  border: 2px solid transparent;
  display: flex;
  width: 200px;
  height: 200px;
  background-color: lightgray;
  border-radius: 50%;
}

.circle img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
  display: block;
}

.circle:hover img {
  transform: scale(1.05);
  transition: transform 0.3s ease;
}

.footer {
  display: flex;
  justify-content: flex-end;
  font-size: 14px;
  padding: 20px;
  color: var(--text-color);
}

.section-title a {
  display: inline-block;
  transition: transform 0.3s ease, color 0.3s ease;
}

.section-title a:hover {
  transform: translateX(4px);
  color: var(--link-hover-color);
}

.popular-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 20px;
  color: var(--text-color);
  margin-top: 10px;
}

.popular-text {
  margin: 0;
  color: var(--text-color);
}

.arrow-link {
  color: var(--link-color);
  font-size: 24px;
  transition: transform 0.3s ease, color 0.3s ease;
}

.arrow-link:hover {
  transform: translateX(5px);
  color: var(--link-hover-color);
}

/* Sidebar container */
.sidebar {
  position: fixed;
  top: 0;
  left: -250px;
  width: 250px;
  height: 100%;
  background-color: var(--background-color);
  padding: 20px;
  box-sizing: border-box;
  transition: left 0.3s ease;
  z-index: 1500;
}

.sidebar.active {
  left: 0;
}

.close-btn {
  font-size: 30px;
  color: var(--text-color);
  background: none;
  border: none;
  cursor: pointer;
  margin-bottom: 20px;
}

.sidebar nav a {
  display: block;
  color: var(--link-color);
  text-decoration: none;
  padding: 12px 0;
  border-bottom: 1px solid var(--border-color);
  font-weight: 500;
  transition: color 0.3s ease;
}

.sidebar nav a:hover {
  color: var(--link-hover-color);
}

/* Switch styles */
.theme-toggle-container {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--text-color);
  font-size: 14px;
}

.theme-label {
  color: var(--text-color);
  font-weight: 500;
}

.switch {
  position: relative;
  display: inline-block;
  width: 54px;
  height: 28px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0; left: 0; right: 0; bottom: 0;
  background-color: #ccc;
  transition: 0.4s;
  border-radius: 28px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 20px; width: 20px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: 0.4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: #4CAF50;
}

input:checked + .slider:before {
  transform: translateX(26px);
}
