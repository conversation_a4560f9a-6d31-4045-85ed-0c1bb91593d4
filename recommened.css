.recommendations {
  display: flex;
  gap: 24px;
  overflow-x: auto;
  padding: 24px;
  scroll-behavior: smooth;
}

.rec-card {
  flex: 0 0 auto;
  width: 160px;
  background:var(--bg-color);
  border-radius: 10px;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.rec-card:hover {
  transform: scale(1.05);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.5);
}

.rec-card img {
  width: 100%;
  display: block;
}

.rec-info {
  padding: 8px;
}

.rec-info h4 {
  margin: 5px 0;
  font-size: 15px;
}

.rec-info p {
  margin: 0;
  font-size: 13px;
  color: #aaa;
}

::-webkit-scrollbar {
  height: 8px;
}

::-webkit-scrollbar-thumb {
  background: #444;
  border-radius: 4px;
}

::-webkit-scrollbar-track {
  background: #222;
}
