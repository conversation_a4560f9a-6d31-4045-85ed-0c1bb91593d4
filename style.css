
:root {
  --bg-color: #121212;
  --text-color: #ffffff;
  --secondary-bg: #222;
  --link-color: #ccc;
  --hover-link-color:  #251670;;
  --border-color: #333;
  --input-bg: transparent;
  --input-border: #ffffff;
}

.light-theme {
  --bg-color: #f0f0f0;
  --text-color: #222222;
  --secondary-bg: #ffffff;
  --link-color: #333333;
  --hover-link-color: #251670;
  --border-color: #dddddd;
  --input-bg: #ffffff;
  --input-border: #333333;
}



* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body, html {
  height: 100%;
  width: 100%;
  background-color: var(--bg-color);
  color: var(--text-color);
  font-family: 'Poppins', sans-serif;
  
}

a {
  text-decoration: none;
}

.container {
  padding: 16px;
  min-height: 10vh;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1px;
  

}

.nav-left {
  display: flex;
  align-items: center;
  gap: 15px;
    
  
}

.home-link {
  /* width: 35px;
  height: 35px; */
  color: var(--text-color); 
  /* Or your preferred color */

}

.search-box input {
  padding: 8px 14px;
  border-radius: 20px;
  border: 2px solid var(--text-color);
  background: transparent;
  color:var(--text-color);
  outline: none;
  display: flex;
  align-items: center;
}

.nav-right a {
  margin-left: 20px;
  color:var(--text-color);
  font-weight: bold;
}

.section-title {
  margin:10px 0 10px 0;
  font-size: 20px;
  color:var(--text-color);
  display: flex;
  align-items: center;
  gap: 10px;
}

.trending {
  
  display: flex;
  gap: 30px;
  margin-bottom: 40px;
  color:var(--text-color);
  padding:16;
}

.box {
  width: 200px;
  height: 200px;
  background-color: var(--secondary-bg);
  border: 2px solid transparent;
}
.box img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;    
  display: block;     
}
.box:hover img {
  transform: scale(1.05);
  transition: transform 0.3s ease;
}


.box.active {
  border: 2px solid #00f;
}

.artists {
  display: flex;
  gap: 30px;
  margin-bottom: 40px;
  padding: 16px;
  margin-left: 10px;
}


.circle {
  border: 2px solid transparent;
  display: flex;
  width: 200px;
  height: 200px;
  background-color: var(--secondary-bg);
  border-radius: 50%;
}
.circle img{
   width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;    
  display: block; 
}
.circle:hover img {
  transform: scale(1.05);
  transition: transform 0.3s ease;
}


.footer {
  display: flex;
  justify-content: flex-end;
  font-size:14px;
  padding: 20px;
}
.trending songs.box {
  width: 200px;
  margin: 100px;
  height: 200px;
  display: flex;
  border: 2px solid transparent;
}
.trending songs.box img{
   width: 100%;
  height: 100%;
  object-fit: cover;
  display:flex ;
  border-radius: 8px;    
  display: block;

}
.trending songs.box img:hover {
  display: flex;
  transform: scale(1.05);
  transition: transform 0.3s ease;
}

;
.artists name.circle{
  width: 200px;
  margin: 100px;
  height: 200px;
  background-color: var(--secondary-bg);
  display: flex;
  border: 2px solid transparent;
}
.artists name.circle img{
   width: 100%;
  height: 100%;
  object-fit: cover;
  display:flex ;
  border-radius: 8px;    
  display: block;

}
.artists name.circle img:hover {
  display: flex;
  transform: scale(1.05);
  transition: transform 0.3s ease;
}
.section-title a {
  display: inline-block;
  text-decoration: none;
  transition: transform 0.3s ease, color 0.3s ease;
}

.section-title a:hover {
  transform: translateX(4px);
  color: var(--hover-link-color); /* Or your preferred hover color */
}
.popular-header {
  display: flex;
  justify-content: space-between; /* Pushes text left, arrow right */ /* Adjust as needed */
  align-items: center;
  font-size: 20px;
  color: var(--text-color);
  margin-top: 10px; /* Adjust as needed */
}

.popular-text {
  margin: 0;
}

.arrow-link {
  color: var(--text-color);
  font-size: 24px;
  transition: transform 0.3s ease, color 0.3s ease;
}

.arrow-link:hover {
  transform: translateX(5px); /* Smooth right slide on hover */
  color:var(--hover-link-color);
}

/* trending songs */
.section-title-text {
  color: var(--text-color);
  font-weight: 600;
  font-size: 18px;
}

.section-title-link {
  text-decoration: none;
  color: var(--text-color);
  font-size: 22px;
  transition: color 0.3s ease;
}

.section-title-link:hover {
  color: var(--hover-link-color);
}



/* Sidebar container */
.sidebar {
  position: fixed;
  top: 0;
  left: -250px; /* Hidden by default */
  width: 250px;
  height: 100%;
  background-color: var(--secondary-bg);
  padding: 20px;
  box-sizing: border-box;
  transition: left 0.3s ease;
  z-index: 1500;
}

/* Show sidebar */
.sidebar.active {
  left: 0;
}

/* Close button */
.close-btn {
  font-size: 30px;
  color: var(--text-color);
  background: none;
  border: none;
  cursor: pointer;
  margin-bottom: 20px;
}

/* Sidebar links */
.sidebar nav a {
  display: block;
  color: var(--link-color);
  text-decoration: none;
  padding: 12px 0;
  border-bottom: 1px solid var(--border-color);
  font-weight: 500;
  transition: color 0.3s;
}

.sidebar nav a:hover {
  color: var(--hover-link-color);
}

/* The switch container */
.switch {
  position: relative;
  /* alignment down centre */
  align-self: self-end;  
  display: inline-block;
  width: 54px;
  height: 28px;
}

/* Hide default checkbox */
.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.theme-toggle-container {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  gap: 8px; /* spacing between text and slider */
  color: var(--text-color);
  font-size: 14px;
}

.theme-label {
  color: #ccc;
  font-weight: 500;
}

/* The slider */
.slider {
  position: absolute;
  cursor: pointer;
  top: 0; left: 0;
  right: 0; bottom: 0;
  background-color: #ccc;
  transition: 0.4s;
  border-radius: 28px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 20px; width: 20px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: 0.4s;
  border-radius: 50%;
}

/* Checked state */
input:checked + .slider {
  background-color: #251670;
}

input:checked + .slider:before {
  transform: translateX(26px);
}

/* Rounded slider */
.slider.round {
  border-radius: 34px;
}

.slider.round:before {
  border-radius: 50%;
}



/* Light theme overrides */
.light-theme {
  background-color: #f0f0f0;
  color: #222;
}

.light-theme .sidebar {
  background-color: #fff;
}

.light-theme .sidebar nav a {
  color: #333;
  border-bottom: 1px solid #ddd;
}

.light-theme .sidebar nav a:hover {
  color:  #251670;
}

.light-theme .close-btn {
  color: #222;
}


.item-title {
  color: var(--text-color);
  text-align: center;
  margin-top: 8px;
  font-size: 14px;
}
:root {
  --text-color: #fff; /* Default for dark mode */
}

.light-theme {
  --text-color: #222; /* For light mode */
}
