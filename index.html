<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Music UI</title>
  <link rel="stylesheet" href="style.css" />
  <link
    rel="stylesheet"
    href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
    integrity="sha512-..."
    crossorigin="anonymous"
  />

  <!-- search bar content -->
  <style>
  #songList {
    position: absolute;
    background-color: var(--secondary-bg);
    color: var(--text-color);
    list-style: none;
    padding: 0;
    margin: 0;
    width: 200px;
    max-height: 150px;
    overflow-y: auto;
    border-radius: 4px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.5);
    display: none;
    z-index: 1000;
  }
  #songList li {
    padding: 6px 10px;
    border-bottom: 1px solid var(--border-color);
  }
  #songList li:last-child {
    border-bottom: none;
  }
  #songList li:hover {
    background-color: var(--secondary-bg);
    cursor: pointer;
  }
  #songList li a {
    color: var(--text-color);
    text-decoration: none;
  }
  .search-box {
    position: relative; /* To position songList absolutely */
  }
</style>

</head>
<body>
  <!-- Sidebar -->
  <div id="sidebar" class="sidebar">
  <button id="closeSidebar" class="close-btn">&times;</button>
  <nav>
    <a href="index.html">Home</a>
    <a href="trending.html">Trending</a>
    <a href="artist.html">Artists</a>
    <a href="login.html">Login</a>
    <a href="signup.html">Sign Up</a>
  </nav>

  <!-- toggle switch -->
   <div class="theme-toggle-container">
  <span class="theme-label">Dark</span>
  <label class="switch">
    <input type="checkbox" id="themeToggle">
    <span class="slider round"></span>
  </label>
  <span class="theme-label">Light</span>
</div>


</div>


  <div class="container">
    <nav class="navbar">
      <div class="nav-left">
        <a href="#" class="home-link">
          <i class="fas fa-home"></i>
        </a>
        <div class="search-box">
          <input type="text" id="searchInput" placeholder="Search..." oninput="filterSongs()" />
          <ul id="songList"></ul>
        </div>
      </div>
      
      <div class="nav-right">
        <a href="signup.html">Sign Up</a>
        <a href="login.html">Log in</a>
      </div>
    </nav>
      
   <h2 class="section-title" style="display: flex; justify-content: space-between; align-items: center; padding: 20px;">
  <span class="section-title-text">Trending songs</span>
<a href="trending.html" class="section-title-link">
  &rarr;
</a>

</h2>

<div class="trending" id="trendingSongs">
  <!-- Trending songs will be inserted here dynamically -->
</div>



<div class="trending" id="trendingSongs">
  <!-- Trending songs will be inserted here dynamically -->
</div>


    </div>

   <div class="popular-header">
  <h2 class="popular-text">Popular artist</h2>
  <a href="artist.html" class="arrow-link">→</a>
</div>

    <div class="artists">
      <div class="circle"><img src="https://i1.sndcdn.com/artworks-000162011489-0qxap7-t500x500.jpg" alt="Music"></div>
      <div class="circle"><img src="https://th.bing.com/th/id/OIP.pfv9AgNCU-4KCbckKYDJSAHaEK?rs=1&pid=ImgDetMain&cb=idpwebpc2" alt="Music"></div> 
      <div class="circle"><img src="https://i.scdn.co/image/****************************************" alt="Music"></div>
      <div class="circle"><img src="https://3.bp.blogspot.com/-aNpRjOPfqq0/VdtEh8xV5HI/AAAAAAAABuo/BiKePV5iavA/s1600/bhaktaraj.jpg" alt="Music"></div>
      <div class="circle"><img src="https://i0.wp.com/www.nepallivetoday.com/wp-content/uploads/2021/06/nabin-k-bhattarai.jpg?fit=1280%2C720&ssl=1" alt="Music"></div>
      <div class="circle"><img src="https://tse4.mm.bing.net/th/id/OIP.SydTLykXMo6na7ECHxgKFQHaHa?r=0&rs=1&pid=ImgDetMain&o=7&rm=3" alt="Music"></div>
    </div>

    <h2 style="padding: 20px; margin: 0;">Recommended For You</h2>
    <link rel="stylesheet" href="recommened.css" />
    <div class="recommendations">
      <div class="rec-card">
        <img src="https://i.ytimg.com/vi/61owSt21ZcM/maxresdefault.jpg" alt="Song 1" />
        <div class="rec-info">
          <h4>Jomsam Mai Bazaar</h4>
          <p>Song · Nepathaya</p>
        </div>
      </div>
      <div class="rec-card">
        <img src="https://is1-ssl.mzstatic.com/image/thumb/Music221/v4/2f/c2/df/2fc2df66-b22c-6d63-05b7-de156265254f/7300342967270.jpg/800x800cc.jpg" alt="Song 2" />
        <div class="rec-info">
          <h4>Jhim Jhimaune Aankha</h4>
          <p>Song · Ekdev Limbu</p>
        </div>
      </div>
      <div class="rec-card">
        <img src="https://i.ytimg.com/vi/31JB2m7pa-s/maxresdefault.jpg" alt="Song 3" />
        <div class="rec-info">
          <h4>Din</h4>
          <p>Song ·Anuprastha</p>
        </div>
      </div>
      <div class="rec-card">
        <img src="https://i.scdn.co/image/ab67616d0000b2734082d8b7b9b6752fe3bac19f" alt="Song 4" />
        <div class="rec-info">
          <h4>Timi Bhane</h4>
          <p>Song · Albatross</p>
        </div>
      </div>
      <!-- Add more recommended songs -->
    </div>

    <footer class="footer">
      <a href="about.html" style="text-decoration: none;padding: 20px;font-size: 28px; font-weight: bold;">About</a>
    </footer>
  </div>

  <script>
    // Static songs data with images
    const songs = [
      { songName: "Rato ra chandra surya", image: "https://img.youtube.com/vi/biufV1olDes/sddefault.jpg" },
      { songName: "Sarangi", image: "https://i.ytimg.com/vi/Sh8ZYHnb86c/maxresdefault.jpg" },
      { songName: "Sali Man paryo", image: "https://cdn-images.dzcdn.net/images/cover/32abdd439186391333cdfa9bddf3f629/0x1900-000000-80-0-0.jpg" },
      { songName: "Preeti Basyo", image: "https://i.scdn.co/image/ab67616d0000b273d0ccf2607b8ffaa7b6d65e9c" },
      { songName: "Samjhi Baschu", image: "https://i.scdn.co/image/ab67616d00001e0287bf35052b1e2e91297f3922" },
      { songName: "Timi Hau Lakhauko Dhadkan", image: "https://i.ytimg.com/vi/h9lFxdIUNmI/maxresdefault.jpg" },
      { songName: "Resham", image: "https://i.ytimg.com/vi/laxLXAkTjRg/maxresdefault.jpg" },
    ];

    const songListEl = document.getElementById("songList");
    const trendingEl = document.getElementById("trendingSongs");

    // Display trending songs dynamically
    function displayTrending() {
      trendingEl.innerHTML = "";
      songs.forEach(song => {
        const div = document.createElement("div");
        div.classList.add("box");
        div.innerHTML = `
          <a href="songDetails.html?songName=${encodeURIComponent(song.songName)}">
            <img src="${song.image}" alt="${song.songName}" width="100%" height="100%">
            <p style="color: white; text-align: center;">${song.songName}</p>
          </a>
        `;
        trendingEl.appendChild(div);
      });
    }

    //for pop artist
    // Static popular artists data
const artists = [
  { name: "Narayan Gopal", image: "https://i1.sndcdn.com/artworks-000162011489-0qxap7-t500x500.jpg" },

  { name: "Shushant KC", image: "https://th.bing.com/th/id/OIP.pfv9AgNCU-4KCbckKYDJSAHaEK?rs=1&pid=ImgDetMain&cb=idpwebpc2" },
  { name: "Sajjan Raj Vaidya", image: "https://i.scdn.co/image/****************************************" },
  { name: "Bhakta Raj Acharya", image: "https://3.bp.blogspot.com/-aNpRjOPfqq0/VdtEh8xV5HI/AAAAAAAABuo/BiKePV5iavA/s1600/bhaktaraj.jpg" },
  { name: "Nabin K Bhattarai", image: "https://i0.wp.com/www.nepallivetoday.com/wp-content/uploads/2021/06/nabin-k-bhattarai.jpg?fit=1280%2C720&ssl=1" },
  { name: "Trisana Gurung", image: "https://tse4.mm.bing.net/th/id/OIP.SydTLykXMo6na7ECHxgKFQHaHa?r=0&rs=1&pid=ImgDetMain&o=7&rm=3" }
];

const artistsEl = document.querySelector(".artists");

// Display popular artists dynamically
function displayArtists() {
  artistsEl.innerHTML = "";
  artists.forEach(artist => {
    const div = document.createElement("div");
    div.classList.add("artist-card");
    div.innerHTML = `
      <a href="artistDetails.html?artistName=${encodeURIComponent(artist.name)}">
        <div class="circle">
          <img src="${artist.image}" alt="${artist.name}">
        </div>
        <p style="color: white; text-align: center; margin-top: 8px;">${artist.name}</p>
      </a>
    `;
    artistsEl.appendChild(div);
  });
}


    // Display filtered songs in search
    function displaySongs(list) {
  if (list.length === 0 || document.getElementById("searchInput").value === "") {
    songListEl.style.display = "none";
    return;
  }

  songListEl.innerHTML = "";
  list.forEach(song => {
    const li = document.createElement("li");
    li.innerHTML = `<a href="songDetails.html?songName=${encodeURIComponent(song.songName)}">${song.songName}</a>`;
    songListEl.appendChild(li);
  });
  songListEl.style.display = "block";
}

function filterSongs() {
  const query = document.getElementById("searchInput").value.toLowerCase();
  const filtered = songs.filter(song =>
    song.songName.toLowerCase().includes(query)
  );
  displaySongs(filtered);
}

// Optional: Hide list when clicking outside
document.addEventListener("click", (e) => {
  if (!document.querySelector(".search-box").contains(e.target)) {
    songListEl.style.display = "none";
  }
});


    function filterSongs() {
      const query = document.getElementById("searchInput").value.toLowerCase();
      const filtered = songs.filter(song =>
        song.songName.toLowerCase().includes(query)
      );
      displaySongs(filtered);
    }

    // Initial load
    displayTrending();
    displaySongs(songs);
    displayArtists();

    //sidebar toggle
      const sidebar = document.getElementById("sidebar");
  const homeIcon = document.querySelector(".home-link");
  const closeBtn = document.getElementById("closeSidebar");

  homeIcon.addEventListener("click", (e) => {
    e.preventDefault(); // prevent default anchor behavior
    sidebar.classList.add("active");
  });

  closeBtn.addEventListener("click", () => {
    sidebar.classList.remove("active");
  });

  // Optional: close sidebar when clicking outside
  document.addEventListener("click", (e) => {
    if (!sidebar.contains(e.target) && !homeIcon.contains(e.target)) {
      sidebar.classList.remove("active");
    }
  });

//   // Theme Toggle
//   const themeToggle = document.getElementById('themeToggle');

// themeToggle.addEventListener('change', () => {
//   document.body.classList.toggle('light-theme');
// });

const themeToggle = document.getElementById('themeToggle');

// Load theme from localStorage on page load
if (localStorage.getItem('theme') === 'light') {
  document.body.classList.add('light-theme');
  themeToggle.checked = true; // set switch ON
}

// Listen for toggle changes
themeToggle.addEventListener('change', () => {
  if (themeToggle.checked) {
    document.body.classList.add('light-theme');
    localStorage.setItem('theme', 'light'); // Save as light
  } else {
    document.body.classList.remove('light-theme');
    localStorage.setItem('theme', 'dark'); // Save as dark
  }
});




  </script>
</body>
</html> 
