* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  background: linear-gradient(135deg, #1e1e2f, #282841);
  font-family: 'Segoe UI', sans-serif;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

.login-container {
  background-color: #ffffff10;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 0 10px #00000055;
  backdrop-filter: blur(10px);
  width: 300px;
  color: #fff;
  text-align: center;
}

.login-container h2 {
  margin-bottom: 20px;
}

.input-group {
  margin-bottom: 20px;
  text-align: left;
}

.input-group label {
  display: block;
  margin-bottom: 5px;
  font-size: 14px;
}

.input-group input {
  width: 100%;
  padding: 10px;
  border: none;
  border-radius: 6px;
  outline: none;
  background-color: #ffffff15;
  color: #fff;
}

.input-group input::placeholder {
  color: #bbb;
}

button {
  width: 100%;
  padding: 12px;
  background-color: #4CAF50;
  border: none;
  border-radius: 6px;
  color: white;
  font-weight: bold;
  cursor: pointer;
  transition: background 0.3s ease;
}

button:hover {
  background-color: #45a049;
}
