import { initializeApp } from "https://www.gstatic.com/firebasejs/11.10.0/firebase-app.js";
import { getAuth, signInWithEmailAndPassword } from "https://www.gstatic.com/firebasejs/11.10.0/firebase-auth.js";

const firebaseConfig = {
  apiKey: "AIzaSyBpNlflcUs1vnuvixELhdAGHpMp9YHjHqE",
  authDomain: "semester-project-70df4.firebaseapp.com",
  projectId: "semester-project-70df4",
  storageBucket: "semester-project-70df4.firebasestorage.app",
  messagingSenderId: "894206249199",
  appId: "1:894206249199:web:385719139290d7fc31b31a",
  measurementId: "G-DW0V9KP83K"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);

const form = document.getElementById("loginForm");
const msg = document.getElementById("loginMessage");

form.addEventListener("submit", async (e) => {
  e.preventDefault();

  const email = document.getElementById("email").value.trim();
  const password = document.getElementById("password").value;

  try {
    await signInWithEmailAndPassword(auth, email, password);
    msg.style.color = "lightgreen";
    msg.textContent = "Login successful!...";
    setTimeout(() => {
      window.location.href = "index.html";
    }, 1500);
  } catch (error) {
    msg.style.color = "salmon";
    msg.textContent = error.message;
  }
});
