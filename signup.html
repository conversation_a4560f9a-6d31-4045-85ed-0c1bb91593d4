<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Sign Up</title>

  <!-- Firebase SDK -->
  <script src="https://www.gstatic.com/firebasejs/10.5.2/firebase-app.js"></script>
  <script src="https://www.gstatic.com/firebasejs/10.5.2/firebase-auth.js"></script>

  <!-- CSS -->
  <link rel="stylesheet" href="login.css" />
  <script type="module">
  // Import the functions you need from the SDKs you need
  import { initializeApp } from "https://www.gstatic.com/firebasejs/11.10.0/firebase-app.js";
  import { getAnalytics } from "https://www.gstatic.com/firebasejs/11.10.0/firebase-analytics.js";
  // TODO: Add SDKs for Firebase products that you want to use
  // https://firebase.google.com/docs/web/setup#available-libraries

  // Your web app's Firebase configuration
  // For Firebase JS SDK v7.20.0 and later, measurementId is optional
  const firebaseConfig = {
    apiKey: "AIzaSyBpNlflcUs1vnuvixELhdAGHpMp9YHjHqE",
    authDomain: "semester-project-70df4.firebaseapp.com",
    projectId: "semester-project-70df4",
    storageBucket: "semester-project-70df4.firebasestorage.app",
    messagingSenderId: "894206249199",
    appId: "1:894206249199:web:385719139290d7fc31b31a",
    measurementId: "G-DW0V9KP83K"
  };

  // Initialize Firebase
  const app = initializeApp(firebaseConfig);
  const analytics = getAnalytics(app);
</script>
</head>
<body>
  <div class="login-container">
    <h2>Sign Up</h2>
    <form id="signupForm">
      <div class="input-group">
        <label for="email">Email</label>
        <input type="email" id="email" placeholder="Enter your email" required />
      </div>
      <div class="input-group">
        <label for="password">Password</label>
        <input type="password" id="password" placeholder="Enter password" required />
      </div>
      <button type="submit">Sign Up</button>
    </form>
    <p id="message"></p>
  </div>

 <script type="module" src="signup.js"></script>

</body>
</html>
