<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Spotify Style Artists</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    body {
      background-color: #121212;
      color: #fff;
      font-family: Arial, sans-serif;
      padding: 20px;
    }
    h1 {
      color: #1DB954;
      text-align: center;
      margin-bottom: 30px;
    }
    .artist-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
      gap: 20px;
    }
    .artist-card {
      background-color: #181818;
      border-radius: 10px;
      overflow: hidden;
      transition: transform 0.3s ease, box-shadow 0.3s ease;
      cursor: pointer;
    }
    .artist-card:hover {
      transform: scale(1.05);
      box-shadow: 0 10px 20px rgba(0,0,0,0.6);
    }
    .artist-image img {
      width: 100%;
      height: 180px;
      object-fit: cover;
      display: block;
    }
    .artist-name {
      padding: 15px;
      text-align: center;
      font-size: 1.1rem;
    }
  </style>
</head>